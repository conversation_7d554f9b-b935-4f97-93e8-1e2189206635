"""
Utility functions and helpers for the data logger
"""

import json
import csv
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import argparse

from .mongodb_client import MongoDBClient
from .config import ConfigManager


def export_data_to_csv(mongodb_client: MongoDBClient, 
                      output_file: str,
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None,
                      node_name: Optional[str] = None) -> bool:
    """
    Export data from MongoDB to CSV file
    
    Args:
        mongodb_client: MongoDB client instance
        output_file: Output CSV file path
        start_time: Start time for data range (optional)
        end_time: End time for data range (optional)
        node_name: Filter by node name (optional)
    
    Returns:
        bool: True if export successful
    """
    try:
        # Get data from MongoDB
        if start_time and end_time:
            data = mongodb_client.get_data_range(start_time, end_time, node_name)
        else:
            data = mongodb_client.get_latest_data(node_name, limit=10000)
        
        if not data:
            print("No data found to export")
            return False
        
        # Write to CSV
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'node_name', 'value', 'node_id', 'source']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for record in data:
                writer.writerow({
                    'timestamp': record.get('timestamp', ''),
                    'node_name': record.get('node_name', ''),
                    'value': record.get('value', ''),
                    'node_id': record.get('node_id', ''),
                    'source': record.get('source', '')
                })
        
        print(f"Exported {len(data)} records to {output_file}")
        return True
        
    except Exception as e:
        print(f"Error exporting data: {e}")
        return False


def export_data_to_json(mongodb_client: MongoDBClient,
                       output_file: str,
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None,
                       node_name: Optional[str] = None) -> bool:
    """
    Export data from MongoDB to JSON file
    
    Args:
        mongodb_client: MongoDB client instance
        output_file: Output JSON file path
        start_time: Start time for data range (optional)
        end_time: End time for data range (optional)
        node_name: Filter by node name (optional)
    
    Returns:
        bool: True if export successful
    """
    try:
        # Get data from MongoDB
        if start_time and end_time:
            data = mongodb_client.get_data_range(start_time, end_time, node_name)
        else:
            data = mongodb_client.get_latest_data(node_name, limit=10000)
        
        if not data:
            print("No data found to export")
            return False
        
        # Convert ObjectId and datetime to string for JSON serialization
        json_data = []
        for record in data:
            json_record = {}
            for key, value in record.items():
                if key == '_id':
                    json_record[key] = str(value)
                elif isinstance(value, datetime):
                    json_record[key] = value.isoformat()
                else:
                    json_record[key] = value
            json_data.append(json_record)
        
        # Write to JSON file
        with open(output_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(json_data, jsonfile, indent=2, ensure_ascii=False)
        
        print(f"Exported {len(data)} records to {output_file}")
        return True
        
    except Exception as e:
        print(f"Error exporting data: {e}")
        return False


def create_sample_config(output_path: str = "config/sample_config.yaml") -> bool:
    """
    Create a sample configuration file
    
    Args:
        output_path: Output file path
    
    Returns:
        bool: True if creation successful
    """
    try:
        from .config import AppConfig, OPCUAConfig, MongoDBConfig, NodeConfig
        
        # Create sample configuration
        sample_nodes = [
            NodeConfig(node_id="ns=2;i=2", name="Temperature", data_type="float"),
            NodeConfig(node_id="ns=2;i=3", name="Pressure", data_type="float"),
            NodeConfig(node_id="ns=2;i=4", name="Flow_Rate", data_type="float")
        ]
        
        opcua_config = OPCUAConfig(
            server_url="opc.tcp://localhost:4840",
            nodes=sample_nodes
        )
        
        mongodb_config = MongoDBConfig(
            connection_string="mongodb://localhost:27017/",
            database="datalogger",
            collection="sensor_data"
        )
        
        config = AppConfig(
            opcua=opcua_config,
            mongodb=mongodb_config
        )
        
        # Save configuration
        config_manager = ConfigManager()
        config_manager.save_config(config, output_path)
        
        print(f"Sample configuration created at {output_path}")
        return True
        
    except Exception as e:
        print(f"Error creating sample config: {e}")
        return False


def validate_config_file(config_path: str) -> bool:
    """
    Validate a configuration file
    
    Args:
        config_path: Path to configuration file
    
    Returns:
        bool: True if configuration is valid
    """
    try:
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()
        print(f"✓ Configuration file {config_path} is valid")
        
        # Print configuration summary
        print(f"  OPC UA Server: {config.opcua.server_url}")
        print(f"  MongoDB Database: {config.mongodb.database}")
        print(f"  Monitored Nodes: {len(config.opcua.nodes)}")
        print(f"  Sampling Interval: {config.datalogger.sampling_interval}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration file {config_path} is invalid: {e}")
        return False


def cli_export_data():
    """CLI tool for exporting data"""
    parser = argparse.ArgumentParser(description="Export data from MongoDB")
    
    parser.add_argument("-c", "--config", default="config/config.yaml",
                       help="Configuration file path")
    parser.add_argument("-o", "--output", required=True,
                       help="Output file path")
    parser.add_argument("-f", "--format", choices=["csv", "json"], default="csv",
                       help="Output format")
    parser.add_argument("-n", "--node", help="Filter by node name")
    parser.add_argument("--start", help="Start time (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--end", help="End time (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--days", type=int, help="Export data from last N days")
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.load_config()
        
        # Connect to MongoDB
        mongodb_client = MongoDBClient(config.mongodb)
        if not mongodb_client.connect():
            print("Failed to connect to MongoDB")
            return 1
        
        # Parse time range
        start_time = None
        end_time = None
        
        if args.days:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=args.days)
        elif args.start and args.end:
            start_time = datetime.strptime(args.start, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(args.end, "%Y-%m-%d %H:%M:%S")
        
        # Export data
        if args.format == "csv":
            success = export_data_to_csv(
                mongodb_client, args.output, start_time, end_time, args.node
            )
        else:
            success = export_data_to_json(
                mongodb_client, args.output, start_time, end_time, args.node
            )
        
        mongodb_client.disconnect()
        return 0 if success else 1
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


def cli_create_config():
    """CLI tool for creating sample configuration"""
    parser = argparse.ArgumentParser(description="Create sample configuration")
    parser.add_argument("-o", "--output", default="config/sample_config.yaml",
                       help="Output configuration file path")
    
    args = parser.parse_args()
    
    success = create_sample_config(args.output)
    return 0 if success else 1


def cli_validate_config():
    """CLI tool for validating configuration"""
    parser = argparse.ArgumentParser(description="Validate configuration file")
    parser.add_argument("config", help="Configuration file path")
    
    args = parser.parse_args()
    
    success = validate_config_file(args.config)
    return 0 if success else 1


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python -m src.utils <command>")
        print("Commands: export, create-config, validate-config")
        sys.exit(1)
    
    command = sys.argv[1]
    sys.argv = [sys.argv[0]] + sys.argv[2:]  # Remove command from argv
    
    if command == "export":
        sys.exit(cli_export_data())
    elif command == "create-config":
        sys.exit(cli_create_config())
    elif command == "validate-config":
        sys.exit(cli_validate_config())
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
