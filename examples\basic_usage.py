#!/usr/bin/env python3
"""
Basic Usage Example for OPC UA Data Logger

This example demonstrates how to use the data logger programmatically.
"""

import asyncio
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.datalogger import DataLogger, create_datalogger
from src.config import ConfigManager, AppConfig, OPCUAConfig, MongoDBConfig, NodeConfig
from loguru import logger


async def basic_example():
    """Basic example of using the data logger"""
    
    # Method 1: Using configuration file
    print("=== Method 1: Using Configuration File ===")
    
    try:
        async with create_datalogger("config/config.yaml") as datalogger:
            # Start the data logger (this will run until interrupted)
            await datalogger.start()
    except FileNotFoundError:
        print("Configuration file not found. Creating programmatic example...")
    except Exception as e:
        print(f"Error with config file method: {e}")
    
    # Method 2: Programmatic configuration
    print("\n=== Method 2: Programmatic Configuration ===")
    
    # Create configuration programmatically
    nodes = [
        NodeConfig(node_id="ns=2;i=2", name="Temperature", data_type="float"),
        NodeConfig(node_id="ns=2;i=3", name="Pressure", data_type="float"),
        NodeConfig(node_id="ns=2;i=4", name="Flow_Rate", data_type="float")
    ]
    
    opcua_config = OPCUAConfig(
        server_url="opc.tcp://localhost:4840",
        nodes=nodes
    )
    
    mongodb_config = MongoDBConfig(
        connection_string="mongodb://localhost:27017/",
        database="datalogger_example",
        collection="sensor_data"
    )
    
    config = AppConfig(
        opcua=opcua_config,
        mongodb=mongodb_config
    )
    
    # Create and run data logger
    datalogger = DataLogger(config)
    
    try:
        await datalogger.start()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        await datalogger.stop()


async def test_connections_example():
    """Example of testing connections before starting the logger"""
    
    print("=== Testing Connections Example ===")
    
    # Load configuration
    try:
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
    except FileNotFoundError:
        print("Config file not found, using default configuration")
        # Use default configuration
        nodes = [NodeConfig(node_id="ns=2;i=2", name="Temperature")]
        opcua_config = OPCUAConfig(server_url="opc.tcp://localhost:4840", nodes=nodes)
        mongodb_config = MongoDBConfig(
            connection_string="mongodb://localhost:27017/",
            database="test_db",
            collection="test_collection"
        )
        config = AppConfig(opcua=opcua_config, mongodb=mongodb_config)
    
    # Test MongoDB connection
    from src.mongodb_client import MongoDBClient
    
    print("Testing MongoDB connection...")
    mongodb_client = MongoDBClient(config.mongodb)
    
    if mongodb_client.connect():
        print("✓ MongoDB connection successful")
        stats = mongodb_client.get_statistics()
        print(f"  Database: {config.mongodb.database}")
        print(f"  Collection: {config.mongodb.collection}")
        print(f"  Documents: {stats.get('document_count', 0)}")
        mongodb_client.disconnect()
    else:
        print("✗ MongoDB connection failed")
        return False
    
    # Test OPC UA connection
    from src.opcua_client import OPCUAClient
    
    print("Testing OPC UA connection...")
    
    def dummy_callback(node_name, value, timestamp):
        print(f"Received data: {node_name} = {value} at {timestamp}")
    
    opcua_client = OPCUAClient(config.opcua, dummy_callback)
    
    try:
        if await opcua_client.connect():
            print("✓ OPC UA connection successful")
            
            # Get server info
            server_info = await opcua_client.get_server_info()
            print(f"  Server URL: {config.opcua.server_url}")
            print(f"  Server State: {server_info.get('state', 'Unknown')}")
            
            # Test reading nodes once
            if config.opcua.nodes:
                print("Testing node reads...")
                data = await opcua_client.read_nodes_once()
                
                for node_name, node_data in data.items():
                    if 'error' in node_data:
                        print(f"  ✗ {node_name}: {node_data['error']}")
                    else:
                        print(f"  ✓ {node_name}: {node_data['value']}")
            
            await opcua_client.disconnect()
            return True
        else:
            print("✗ OPC UA connection failed")
            return False
    except Exception as e:
        print(f"✗ OPC UA connection error: {e}")
        return False


async def data_export_example():
    """Example of exporting data from MongoDB"""
    
    print("=== Data Export Example ===")
    
    from src.mongodb_client import MongoDBClient
    from src.utils import export_data_to_csv, export_data_to_json
    from datetime import datetime, timedelta
    
    # Load configuration
    try:
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
    except FileNotFoundError:
        print("Config file not found, using default configuration")
        mongodb_config = MongoDBConfig(
            connection_string="mongodb://localhost:27017/",
            database="datalogger",
            collection="sensor_data"
        )
        config = type('Config', (), {'mongodb': mongodb_config})()
    
    # Connect to MongoDB
    mongodb_client = MongoDBClient(config.mongodb)
    
    if not mongodb_client.connect():
        print("Failed to connect to MongoDB")
        return
    
    try:
        # Get latest data
        print("Getting latest 10 records...")
        latest_data = mongodb_client.get_latest_data(limit=10)
        
        if latest_data:
            print(f"Found {len(latest_data)} records")
            for record in latest_data[:3]:  # Show first 3 records
                print(f"  {record.get('timestamp')}: {record.get('node_name')} = {record.get('value')}")
        else:
            print("No data found in database")
        
        # Export data to CSV
        print("\nExporting data to CSV...")
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)  # Last 24 hours
        
        success = export_data_to_csv(
            mongodb_client,
            "exported_data.csv",
            start_time,
            end_time
        )
        
        if success:
            print("✓ Data exported to exported_data.csv")
        else:
            print("✗ Failed to export data")
        
        # Export data to JSON
        print("Exporting data to JSON...")
        success = export_data_to_json(
            mongodb_client,
            "exported_data.json",
            start_time,
            end_time
        )
        
        if success:
            print("✓ Data exported to exported_data.json")
        else:
            print("✗ Failed to export data")
    
    finally:
        mongodb_client.disconnect()


async def custom_data_handler_example():
    """Example of using custom data handling"""
    
    print("=== Custom Data Handler Example ===")
    
    from src.opcua_client import OPCUAClient, OPCUAConfig, NodeConfig
    from datetime import datetime
    
    # Custom data handler
    def custom_data_handler(node_name: str, value, timestamp: datetime):
        """Custom handler for processing OPC UA data"""
        print(f"Custom handler: {node_name} = {value} at {timestamp}")
        
        # Add custom processing logic here
        if node_name == "Temperature" and isinstance(value, (int, float)):
            if value > 50:
                print(f"⚠️  High temperature alert: {value}°C")
            elif value < 0:
                print(f"❄️  Low temperature alert: {value}°C")
        
        # You could also:
        # - Send alerts
        # - Trigger actions
        # - Apply data transformations
        # - Log to custom systems
    
    # Configure OPC UA client with custom handler
    nodes = [
        NodeConfig(node_id="ns=2;i=2", name="Temperature", data_type="float"),
        NodeConfig(node_id="ns=2;i=3", name="Pressure", data_type="float")
    ]
    
    opcua_config = OPCUAConfig(
        server_url="opc.tcp://localhost:4840",
        nodes=nodes
    )
    
    opcua_client = OPCUAClient(opcua_config, custom_data_handler)
    
    try:
        if await opcua_client.connect():
            print("Connected to OPC UA server")
            
            # Setup subscription for real-time data
            if await opcua_client.setup_subscription():
                print("Subscription setup successful")
                print("Listening for data changes... (Press Ctrl+C to stop)")
                
                # Keep running to receive data
                try:
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    print("\nStopping...")
            else:
                print("Failed to setup subscription")
        else:
            print("Failed to connect to OPC UA server")
    
    finally:
        await opcua_client.disconnect()


async def main():
    """Main function to run examples"""
    
    print("OPC UA Data Logger Examples")
    print("=" * 40)
    
    examples = {
        "1": ("Test Connections", test_connections_example),
        "2": ("Data Export", data_export_example),
        "3": ("Custom Data Handler", custom_data_handler_example),
        "4": ("Basic Usage (Full Logger)", basic_example),
    }
    
    print("\nAvailable examples:")
    for key, (name, _) in examples.items():
        print(f"  {key}. {name}")
    
    choice = input("\nSelect example to run (1-4): ").strip()
    
    if choice in examples:
        name, func = examples[choice]
        print(f"\nRunning: {name}")
        print("-" * 40)
        await func()
    else:
        print("Invalid choice")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")
