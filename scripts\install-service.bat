@echo off
REM Install OPC UA Data Logger as Windows Service
REM Requires <PERSON><PERSON><PERSON> (Non-Sucking Service Manager)

setlocal enabledelayedexpansion

echo ========================================
echo OPC UA Data Logger Service Installation
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Check if NSSM is available
nssm version >nul 2>&1
if errorlevel 1 (
    echo ERROR: NSSM (Non-Sucking Service Manager) is not installed
    echo.
    echo Please download NSSM from: https://nssm.cc/download
    echo Extract nssm.exe to a directory in your PATH or to this folder
    echo.
    pause
    exit /b 1
)

echo NSSM found:
nssm version

REM Get current directory
set "CURRENT_DIR=%~dp0"
set "APP_DIR=%CURRENT_DIR%.."
set "SERVICE_NAME=OPCUADataLogger"

echo.
echo Service configuration:
echo   Name: %SERVICE_NAME%
echo   Directory: %APP_DIR%
echo.

REM Check if service already exists
sc query "%SERVICE_NAME%" >nul 2>&1
if not errorlevel 1 (
    echo Service already exists. Removing old service...
    nssm stop "%SERVICE_NAME%"
    nssm remove "%SERVICE_NAME%" confirm
    timeout /t 2 /nobreak >nul
)

REM Install service
echo Installing service...
nssm install "%SERVICE_NAME%" "%APP_DIR%\venv\Scripts\python.exe" "%APP_DIR%\main.py"

if errorlevel 1 (
    echo ERROR: Failed to install service
    pause
    exit /b 1
)

REM Configure service
echo Configuring service...

REM Set working directory
nssm set "%SERVICE_NAME%" AppDirectory "%APP_DIR%"

REM Set service description
nssm set "%SERVICE_NAME%" Description "OPC UA to MongoDB Data Logger Service"

REM Set startup type to automatic
nssm set "%SERVICE_NAME%" Start SERVICE_AUTO_START

REM Configure logging
nssm set "%SERVICE_NAME%" AppStdout "%APP_DIR%\logs\service-stdout.log"
nssm set "%SERVICE_NAME%" AppStderr "%APP_DIR%\logs\service-stderr.log"

REM Set log rotation
nssm set "%SERVICE_NAME%" AppRotateFiles 1
nssm set "%SERVICE_NAME%" AppRotateOnline 1
nssm set "%SERVICE_NAME%" AppRotateSeconds 86400
nssm set "%SERVICE_NAME%" AppRotateBytes 10485760

REM Set environment variables
nssm set "%SERVICE_NAME%" AppEnvironmentExtra "PYTHONPATH=%APP_DIR%" "PYTHONUNBUFFERED=1"

REM Configure service recovery
nssm set "%SERVICE_NAME%" AppExit Default Restart
nssm set "%SERVICE_NAME%" AppRestartDelay 5000

echo Service installed and configured successfully!
echo.

REM Ask if user wants to start the service now
set /p START_NOW="Start the service now? (y/n): "
if /i "!START_NOW!"=="y" (
    echo Starting service...
    nssm start "%SERVICE_NAME%"
    
    if errorlevel 1 (
        echo ERROR: Failed to start service
        echo Check the logs in %APP_DIR%\logs\
    ) else (
        echo Service started successfully!
    )
) else (
    echo Service installed but not started.
)

echo.
echo ========================================
echo Service Management Commands:
echo ========================================
echo Start service:    nssm start "%SERVICE_NAME%"
echo Stop service:     nssm stop "%SERVICE_NAME%"
echo Restart service:  nssm restart "%SERVICE_NAME%"
echo Remove service:   nssm remove "%SERVICE_NAME%" confirm
echo Service status:   sc query "%SERVICE_NAME%"
echo.
echo Logs location: %APP_DIR%\logs\
echo Configuration: %APP_DIR%\config\config.yaml
echo.

pause
