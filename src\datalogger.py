"""
Data Logger Core Module

This module coordinates data collection from OPC UA and storage to MongoDB.
"""

import asyncio
import signal
import sys
from typing import Any, Optional
from datetime import datetime
from contextlib import asynccontextmanager
import logging

from loguru import logger
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from .config import AppConfig, ConfigManager
from .opcua_client import OPCUAClient
from .mongodb_client import MongoDBClient


class DataLogger:
    """Main data logger class that coordinates OPC UA and MongoDB operations"""
    
    def __init__(self, config: AppConfig):
        """
        Initialize Data Logger
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.opcua_client: Optional[OPCUAClient] = None
        self.mongodb_client: Optional[MongoDBClient] = None
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup logging
        self._setup_logging()
        
        # Initialize clients
        self._initialize_clients()
        
        # Setup signal handlers
        self._setup_signal_handlers()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Remove default logger
        logger.remove()
        
        # Add console logger
        logger.add(
            sys.stdout,
            level=self.config.logging.level,
            format=self.config.logging.format,
            colorize=True
        )
        
        # Add file loggers
        for log_name, log_path in self.config.logging.files.items():
            logger.add(
                log_path,
                level=self.config.logging.level,
                format=self.config.logging.format,
                rotation=self.config.logging.rotation,
                retention=self.config.logging.retention,
                compression=self.config.logging.compression,
                enqueue=True
            )
        
        # Setup standard logging to use loguru
        logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    def _initialize_clients(self):
        """Initialize OPC UA and MongoDB clients"""
        try:
            # Initialize MongoDB client
            self.mongodb_client = MongoDBClient(self.config.mongodb)
            
            # Initialize OPC UA client with data callback
            self.opcua_client = OPCUAClient(
                self.config.opcua,
                self._handle_opcua_data
            )
            
            logger.info("Clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize clients: {e}")
            raise
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _handle_opcua_data(self, node_name: str, value: Any, timestamp: datetime):
        """
        Handle data received from OPC UA client
        
        Args:
            node_name: Name of the OPC UA node
            value: Data value
            timestamp: Timestamp of the data
        """
        try:
            if self.mongodb_client and self.mongodb_client.is_connected:
                # Store data in MongoDB
                success = self.mongodb_client.store_data(
                    node_name=node_name,
                    value=value,
                    timestamp=timestamp,
                    source="opcua"
                )
                
                if success:
                    logger.debug(f"Stored data: {node_name} = {value} at {timestamp}")
                else:
                    logger.warning(f"Failed to store data: {node_name} = {value}")
            else:
                logger.warning("MongoDB client not connected, data not stored")
                
        except Exception as e:
            logger.error(f"Error handling OPC UA data: {e}")
    
    async def connect_clients(self) -> bool:
        """
        Connect to OPC UA server and MongoDB
        
        Returns:
            bool: True if both connections successful
        """
        try:
            logger.info("Connecting to services...")
            
            # Connect to MongoDB
            if not self.mongodb_client.connect():
                logger.error("Failed to connect to MongoDB")
                return False
            
            # Connect to OPC UA server
            if not await self.opcua_client.connect():
                logger.error("Failed to connect to OPC UA server")
                return False
            
            # Setup OPC UA subscription
            if not await self.opcua_client.setup_subscription():
                logger.error("Failed to setup OPC UA subscription")
                return False
            
            logger.info("Successfully connected to all services")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to services: {e}")
            return False
    
    async def disconnect_clients(self):
        """Disconnect from OPC UA server and MongoDB"""
        try:
            logger.info("Disconnecting from services...")
            
            if self.opcua_client:
                await self.opcua_client.disconnect()
            
            if self.mongodb_client:
                self.mongodb_client.disconnect()
            
            logger.info("Disconnected from all services")
            
        except Exception as e:
            logger.error(f"Error disconnecting from services: {e}")
    
    def _setup_scheduler(self):
        """Setup periodic tasks scheduler"""
        self.scheduler = AsyncIOScheduler()
        
        # Add periodic buffer flush task
        self.scheduler.add_job(
            self._flush_buffer_task,
            trigger=IntervalTrigger(seconds=self.config.datalogger.flush_interval),
            id='flush_buffer',
            name='Flush MongoDB Buffer'
        )
        
        # Add periodic health check task
        self.scheduler.add_job(
            self._health_check_task,
            trigger=IntervalTrigger(seconds=60),  # Every minute
            id='health_check',
            name='Health Check'
        )
        
        # Add periodic statistics logging
        self.scheduler.add_job(
            self._log_statistics_task,
            trigger=IntervalTrigger(seconds=300),  # Every 5 minutes
            id='log_statistics',
            name='Log Statistics'
        )
    
    async def _flush_buffer_task(self):
        """Periodic task to flush MongoDB buffer"""
        try:
            if self.mongodb_client and self.mongodb_client.is_connected:
                if not self.mongodb_client.buffer.is_empty():
                    success = self.mongodb_client.flush_buffer()
                    if success:
                        logger.debug("Buffer flushed successfully")
                    else:
                        logger.warning("Failed to flush buffer")
        except Exception as e:
            logger.error(f"Error in flush buffer task: {e}")
    
    async def _health_check_task(self):
        """Periodic health check task"""
        try:
            # Check OPC UA connection
            opcua_healthy = (
                self.opcua_client and 
                self.opcua_client.is_connected
            )
            
            # Check MongoDB connection
            mongodb_healthy = (
                self.mongodb_client and 
                self.mongodb_client.is_connected
            )
            
            if not opcua_healthy:
                logger.warning("OPC UA client is not healthy")
                # Attempt reconnection
                if self.opcua_client:
                    logger.info("Attempting to reconnect to OPC UA server...")
                    await self.opcua_client.connect()
                    await self.opcua_client.setup_subscription()
            
            if not mongodb_healthy:
                logger.warning("MongoDB client is not healthy")
                # Attempt reconnection
                if self.mongodb_client:
                    logger.info("Attempting to reconnect to MongoDB...")
                    self.mongodb_client.connect()
            
        except Exception as e:
            logger.error(f"Error in health check task: {e}")
    
    async def _log_statistics_task(self):
        """Periodic task to log statistics"""
        try:
            if self.mongodb_client and self.mongodb_client.is_connected:
                stats = self.mongodb_client.get_statistics()
                buffer_size = self.mongodb_client.buffer.size()
                
                logger.info(
                    f"Statistics - Documents: {stats.get('document_count', 0)}, "
                    f"Buffer size: {buffer_size}, "
                    f"Storage size: {stats.get('storage_size', 0)} bytes"
                )
        except Exception as e:
            logger.error(f"Error in statistics task: {e}")
    
    async def start(self):
        """Start the data logger"""
        try:
            logger.info("Starting Data Logger...")
            
            # Connect to services
            if not await self.connect_clients():
                raise RuntimeError("Failed to connect to required services")
            
            # Setup and start scheduler
            self._setup_scheduler()
            self.scheduler.start()
            
            self.is_running = True
            logger.info("Data Logger started successfully")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"Error starting data logger: {e}")
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """Stop the data logger"""
        try:
            logger.info("Stopping Data Logger...")
            
            self.is_running = False
            
            # Stop scheduler
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=True)
            
            # Disconnect from services
            await self.disconnect_clients()
            
            logger.info("Data Logger stopped")
            
        except Exception as e:
            logger.error(f"Error stopping data logger: {e}")
    
    async def shutdown(self):
        """Initiate graceful shutdown"""
        logger.info("Initiating graceful shutdown...")
        self.shutdown_event.set()


class InterceptHandler(logging.Handler):
    """Handler to intercept standard logging and redirect to loguru"""
    
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


@asynccontextmanager
async def create_datalogger(config_path: Optional[str] = None):
    """
    Context manager for creating and managing a data logger
    
    Args:
        config_path: Path to configuration file
    
    Yields:
        DataLogger: Configured data logger instance
    """
    config_manager = ConfigManager(config_path)
    config = config_manager.load_config()
    
    datalogger = DataLogger(config)
    
    try:
        yield datalogger
    finally:
        if datalogger.is_running:
            await datalogger.stop()
