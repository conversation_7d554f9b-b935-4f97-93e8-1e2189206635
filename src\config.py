"""
Configuration Management Module

This module handles loading and validation of configuration from various sources.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

from .opcua_client import OPCUAConfig, NodeConfig
from .mongodb_client import MongoDBConfig


class LoggingConfig(BaseModel):
    """Logging configuration"""
    level: str = Field(default="INFO", description="Log level")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        description="Log format"
    )
    rotation: str = Field(default="1 day", description="Log rotation")
    retention: str = Field(default="30 days", description="Log retention")
    compression: str = Field(default="gz", description="Log compression")
    
    files: Dict[str, str] = Field(default_factory=lambda: {
        "main": "logs/datalogger.log",
        "opcua": "logs/opcua.log",
        "mongodb": "logs/mongodb.log"
    })
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Invalid log level. Must be one of: {valid_levels}')
        return v.upper()


class DataLoggerConfig(BaseModel):
    """Data logger specific configuration"""
    sampling_interval: int = Field(default=5, description="Sampling interval in seconds")
    buffer_size: int = Field(default=1000, description="Buffer size for batching")
    flush_interval: int = Field(default=30, description="Buffer flush interval in seconds")
    
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: int = Field(default=5, description="Retry delay in seconds")
    
    validate_data: bool = Field(default=True, description="Enable data validation")
    skip_invalid_data: bool = Field(default=True, description="Skip invalid data points")
    
    use_server_timestamp: bool = Field(default=True, description="Use server timestamp")
    timezone: str = Field(default="UTC", description="Timezone for timestamps")
    
    @validator('sampling_interval')
    def validate_sampling_interval(cls, v):
        if v <= 0:
            raise ValueError('Sampling interval must be positive')
        return v
    
    @validator('buffer_size')
    def validate_buffer_size(cls, v):
        if v <= 0:
            raise ValueError('Buffer size must be positive')
        return v


class AppConfig(BaseModel):
    """Main application configuration"""
    opcua: OPCUAConfig
    mongodb: MongoDBConfig
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    datalogger: DataLoggerConfig = Field(default_factory=DataLoggerConfig)


class ConfigManager:
    """Configuration manager for loading and validating configuration"""
    
    def __init__(self, config_path: Optional[str] = None, env_file: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_path: Path to YAML configuration file
            env_file: Path to environment file
        """
        self.config_path = config_path or "config/config.yaml"
        self.env_file = env_file or ".env"
        self.config: Optional[AppConfig] = None
        
        # Load environment variables
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)
    
    def load_config(self) -> AppConfig:
        """
        Load configuration from file and environment variables
        
        Returns:
            AppConfig: Validated configuration object
        """
        # Load from YAML file
        config_data = self._load_yaml_config()
        
        # Override with environment variables
        config_data = self._apply_env_overrides(config_data)
        
        # Validate and create config object
        self.config = AppConfig(**config_data)
        
        return self.config
    
    def _load_yaml_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        config_path = Path(self.config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                config_data = yaml.safe_load(file)
                
            if not config_data:
                raise ValueError("Configuration file is empty")
                
            return config_data
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in configuration file: {e}")
        except Exception as e:
            raise ValueError(f"Error reading configuration file: {e}")
    
    def _apply_env_overrides(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration"""
        
        # OPC UA overrides
        if 'opcua' not in config_data:
            config_data['opcua'] = {}
        
        opcua_config = config_data['opcua']
        
        if os.getenv('OPCUA_SERVER_URL'):
            opcua_config['server_url'] = os.getenv('OPCUA_SERVER_URL')
        
        if os.getenv('OPCUA_USERNAME'):
            opcua_config['username'] = os.getenv('OPCUA_USERNAME')
        
        if os.getenv('OPCUA_PASSWORD'):
            opcua_config['password'] = os.getenv('OPCUA_PASSWORD')
        
        # MongoDB overrides
        if 'mongodb' not in config_data:
            config_data['mongodb'] = {}
        
        mongodb_config = config_data['mongodb']
        
        if os.getenv('MONGODB_CONNECTION_STRING'):
            mongodb_config['connection_string'] = os.getenv('MONGODB_CONNECTION_STRING')
        
        if os.getenv('MONGODB_DATABASE'):
            mongodb_config['database'] = os.getenv('MONGODB_DATABASE')
        
        if os.getenv('MONGODB_COLLECTION'):
            mongodb_config['collection'] = os.getenv('MONGODB_COLLECTION')
        
        # Logging overrides
        if 'logging' not in config_data:
            config_data['logging'] = {}
        
        logging_config = config_data['logging']
        
        if os.getenv('LOG_LEVEL'):
            logging_config['level'] = os.getenv('LOG_LEVEL')
        
        if os.getenv('LOG_ROTATION'):
            logging_config['rotation'] = os.getenv('LOG_ROTATION')
        
        if os.getenv('LOG_RETENTION'):
            logging_config['retention'] = os.getenv('LOG_RETENTION')
        
        # Data logger overrides
        if 'datalogger' not in config_data:
            config_data['datalogger'] = {}
        
        datalogger_config = config_data['datalogger']
        
        if os.getenv('SAMPLING_INTERVAL'):
            try:
                datalogger_config['sampling_interval'] = int(os.getenv('SAMPLING_INTERVAL'))
            except ValueError:
                pass
        
        if os.getenv('BUFFER_SIZE'):
            try:
                datalogger_config['buffer_size'] = int(os.getenv('BUFFER_SIZE'))
            except ValueError:
                pass
        
        if os.getenv('FLUSH_INTERVAL'):
            try:
                datalogger_config['flush_interval'] = int(os.getenv('FLUSH_INTERVAL'))
            except ValueError:
                pass
        
        return config_data
    
    def get_config(self) -> AppConfig:
        """
        Get current configuration
        
        Returns:
            AppConfig: Current configuration object
        """
        if self.config is None:
            self.config = self.load_config()
        
        return self.config
    
    def reload_config(self) -> AppConfig:
        """
        Reload configuration from file
        
        Returns:
            AppConfig: Reloaded configuration object
        """
        self.config = None
        return self.load_config()
    
    def validate_config(self) -> bool:
        """
        Validate current configuration
        
        Returns:
            bool: True if configuration is valid
        """
        try:
            if self.config is None:
                self.load_config()
            return True
        except Exception:
            return False
    
    def save_config(self, config: AppConfig, output_path: Optional[str] = None):
        """
        Save configuration to YAML file
        
        Args:
            config: Configuration object to save
            output_path: Output file path (optional)
        """
        output_path = output_path or self.config_path
        
        # Convert config to dictionary
        config_dict = config.dict()
        
        # Create directory if it doesn't exist
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Write to YAML file
        with open(output_file, 'w', encoding='utf-8') as file:
            yaml.dump(config_dict, file, default_flow_style=False, indent=2)


def load_config(config_path: Optional[str] = None, env_file: Optional[str] = None) -> AppConfig:
    """
    Convenience function to load configuration
    
    Args:
        config_path: Path to configuration file
        env_file: Path to environment file
    
    Returns:
        AppConfig: Loaded configuration
    """
    manager = ConfigManager(config_path, env_file)
    return manager.load_config()
