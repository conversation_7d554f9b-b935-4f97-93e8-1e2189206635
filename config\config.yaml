# OPC UA Server Configuration
opcua:
  server_url: "opc.tcp://localhost:4840"
  security_policy: "None"  # None, Basic256Sha256, etc.
  security_mode: "None"    # None, Sign, SignAndEncrypt
  username: null
  password: null
  certificate_path: null
  private_key_path: null
  
  # Subscription settings
  subscription:
    publishing_interval: 1000  # milliseconds
    lifetime_count: 6000
    max_keepalive_count: 3000
    max_notifications_per_publish: 0
    priority: 0

  # Nodes to monitor (NodeId format)
  nodes:
    - node_id: "ns=2;i=2"
      name: "Temperature"
      data_type: "float"
    - node_id: "ns=2;i=3"
      name: "Pressure"
      data_type: "float"
    - node_id: "ns=2;i=4"
      name: "Flow_Rate"
      data_type: "float"

# MongoDB Configuration
mongodb:
  connection_string: "mongodb://localhost:27017/"
  database: "datalogger"
  collection: "opcda_data"
  
  # Connection pool settings
  max_pool_size: 10
  min_pool_size: 1
  max_idle_time_ms: 30000
  
  # Write concern
  write_concern:
    w: 1
    j: true
    wtimeout: 5000

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "30 days"
  compression: "gz"
  
  # Log files
  files:
    main: "logs/datalogger.log"
    opcua: "logs/opcua.log"
    mongodb: "logs/mongodb.log"

# Data Logger Settings
datalogger:
  # Sampling interval in seconds
  sampling_interval: 5
  
  # Buffer settings
  buffer_size: 1000
  flush_interval: 30  # seconds
  
  # Error handling
  max_retries: 3
  retry_delay: 5  # seconds
  
  # Data validation
  validate_data: true
  skip_invalid_data: true
  
  # Timestamp settings
  use_server_timestamp: true
  timezone: "UTC"
