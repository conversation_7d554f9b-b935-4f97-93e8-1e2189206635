[Unit]
Description=OPC UA to MongoDB Data Logger
After=network.target mongodb.service
Wants=mongodb.service

[Service]
Type=simple
User=datalogger
Group=datalogger
WorkingDirectory=/opt/opcua-datalogger
ExecStart=/opt/opcua-datalogger/venv/bin/python main.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=opcua-datalogger

# Environment
Environment=PYTHONPATH=/opt/opcua-datalogger
Environment=PYTHONUNBUFFERED=1

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/opcua-datalogger/logs
ReadWritePaths=/opt/opcua-datalogger/config

# Resource limits
LimitNOFILE=65536
MemoryMax=1G

[Install]
WantedBy=multi-user.target
