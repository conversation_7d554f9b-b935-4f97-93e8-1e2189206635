"""
OPC UA Client Module

This module provides functionality to connect to OPC UA servers and read data.
"""

import asyncio
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import logging

from asyncua import Client, Node
from asyncua.common.subscription import SubHandler
from asyncua.common.node import Node as NodeType
from pydantic import BaseModel, Field


class NodeConfig(BaseModel):
    """Configuration for OPC UA node monitoring"""
    node_id: str = Field(..., description="OPC UA Node ID")
    name: str = Field(..., description="Human readable name for the node")
    data_type: str = Field(default="variant", description="Expected data type")


class OPCUAConfig(BaseModel):
    """OPC UA Client Configuration"""
    server_url: str = Field(..., description="OPC UA server URL")
    security_policy: str = Field(default="None", description="Security policy")
    security_mode: str = Field(default="None", description="Security mode")
    username: Optional[str] = Field(default=None, description="Username for authentication")
    password: Optional[str] = Field(default=None, description="Password for authentication")
    certificate_path: Optional[str] = Field(default=None, description="Client certificate path")
    private_key_path: Optional[str] = Field(default=None, description="Private key path")
    
    subscription: Dict[str, Any] = Field(default_factory=lambda: {
        "publishing_interval": 1000,
        "lifetime_count": 6000,
        "max_keepalive_count": 3000,
        "max_notifications_per_publish": 0,
        "priority": 0
    })
    
    nodes: List[NodeConfig] = Field(default_factory=list, description="Nodes to monitor")


class DataChangeHandler(SubHandler):
    """Handler for OPC UA data change notifications"""
    
    def __init__(self, callback: Callable[[str, Any, datetime], None]):
        """
        Initialize the handler
        
        Args:
            callback: Function to call when data changes (node_name, value, timestamp)
        """
        self.callback = callback
        self.logger = logging.getLogger(__name__)
    
    def datachange_notification(self, node: NodeType, val: Any, data):
        """Handle data change notifications"""
        try:
            # Get node name from the node's display name or use node id
            node_name = str(node.nodeid)
            timestamp = datetime.utcnow()
            
            if hasattr(data, 'monitored_item') and hasattr(data.monitored_item, 'Value'):
                if hasattr(data.monitored_item.Value, 'ServerTimestamp'):
                    timestamp = data.monitored_item.Value.ServerTimestamp
                elif hasattr(data.monitored_item.Value, 'SourceTimestamp'):
                    timestamp = data.monitored_item.Value.SourceTimestamp
            
            self.logger.debug(f"Data change for {node_name}: {val} at {timestamp}")
            self.callback(node_name, val, timestamp)
            
        except Exception as e:
            self.logger.error(f"Error handling data change: {e}")


class OPCUAClient:
    """OPC UA Client for data collection"""
    
    def __init__(self, config: OPCUAConfig, data_callback: Callable[[str, Any, datetime], None]):
        """
        Initialize OPC UA Client
        
        Args:
            config: OPC UA configuration
            data_callback: Callback function for data changes
        """
        self.config = config
        self.data_callback = data_callback
        self.client = None
        self.subscription = None
        self.monitored_nodes = {}
        self.logger = logging.getLogger(__name__)
        self.is_connected = False
        
    async def connect(self) -> bool:
        """
        Connect to OPC UA server
        
        Returns:
            bool: True if connection successful
        """
        try:
            self.logger.info(f"Connecting to OPC UA server: {self.config.server_url}")
            
            self.client = Client(url=self.config.server_url)
            
            # Set security settings if provided
            if self.config.certificate_path and self.config.private_key_path:
                await self.client.set_security(
                    self.config.security_policy,
                    self.config.certificate_path,
                    self.config.private_key_path,
                    mode=self.config.security_mode
                )
            
            # Set authentication if provided
            if self.config.username and self.config.password:
                self.client.set_user(self.config.username)
                self.client.set_password(self.config.password)
            
            await self.client.connect()
            self.is_connected = True
            self.logger.info("Successfully connected to OPC UA server")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to OPC UA server: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from OPC UA server"""
        try:
            if self.subscription:
                await self.subscription.delete()
                self.subscription = None
                
            if self.client and self.is_connected:
                await self.client.disconnect()
                self.is_connected = False
                self.logger.info("Disconnected from OPC UA server")
                
        except Exception as e:
            self.logger.error(f"Error during disconnect: {e}")
    
    async def setup_subscription(self) -> bool:
        """
        Setup subscription for monitoring nodes
        
        Returns:
            bool: True if subscription setup successful
        """
        try:
            if not self.is_connected:
                self.logger.error("Not connected to OPC UA server")
                return False
            
            # Create subscription
            self.subscription = await self.client.create_subscription(
                period=self.config.subscription["publishing_interval"],
                handler=DataChangeHandler(self._handle_data_change)
            )
            
            # Subscribe to nodes
            for node_config in self.config.nodes:
                try:
                    node = self.client.get_node(node_config.node_id)
                    handle = await self.subscription.subscribe_data_change(node)
                    self.monitored_nodes[node_config.node_id] = {
                        'node': node,
                        'handle': handle,
                        'config': node_config
                    }
                    self.logger.info(f"Subscribed to node: {node_config.name} ({node_config.node_id})")
                    
                except Exception as e:
                    self.logger.error(f"Failed to subscribe to node {node_config.node_id}: {e}")
            
            return len(self.monitored_nodes) > 0
            
        except Exception as e:
            self.logger.error(f"Failed to setup subscription: {e}")
            return False
    
    def _handle_data_change(self, node_id: str, value: Any, timestamp: datetime):
        """Handle data change from subscription"""
        try:
            # Find the node configuration
            node_config = None
            for config in self.config.nodes:
                if config.node_id in node_id:
                    node_config = config
                    break
            
            if node_config:
                self.data_callback(node_config.name, value, timestamp)
            else:
                self.data_callback(node_id, value, timestamp)
                
        except Exception as e:
            self.logger.error(f"Error in data change handler: {e}")
    
    async def read_nodes_once(self) -> Dict[str, Any]:
        """
        Read all configured nodes once
        
        Returns:
            Dict containing node values
        """
        results = {}
        
        if not self.is_connected:
            self.logger.error("Not connected to OPC UA server")
            return results
        
        for node_config in self.config.nodes:
            try:
                node = self.client.get_node(node_config.node_id)
                value = await node.read_value()
                results[node_config.name] = {
                    'value': value,
                    'timestamp': datetime.utcnow(),
                    'node_id': node_config.node_id
                }
                
            except Exception as e:
                self.logger.error(f"Failed to read node {node_config.node_id}: {e}")
                results[node_config.name] = {
                    'value': None,
                    'error': str(e),
                    'timestamp': datetime.utcnow(),
                    'node_id': node_config.node_id
                }
        
        return results
    
    async def get_server_info(self) -> Dict[str, Any]:
        """Get server information"""
        if not self.is_connected:
            return {}
        
        try:
            server_info = {
                'server_url': self.config.server_url,
                'state': await self.client.get_server_state(),
                'build_info': await self.client.get_build_info(),
                'namespace_array': await self.client.get_namespace_array()
            }
            return server_info
            
        except Exception as e:
            self.logger.error(f"Failed to get server info: {e}")
            return {}
