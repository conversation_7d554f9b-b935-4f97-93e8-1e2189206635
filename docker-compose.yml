version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: datalogger-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: datalogger
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - datalogger-network

  # OPC UA Server (for testing)
  opcua-server:
    image: opcfoundation/ua-netstandard:latest
    container_name: datalogger-opcua-server
    restart: unless-stopped
    ports:
      - "4840:4840"
    environment:
      - OPCUA_ENDPOINT=opc.tcp://0.0.0.0:4840
    networks:
      - datalogger-network

  # Data Logger Application
  datalogger:
    build: .
    container_name: datalogger-app
    restart: unless-stopped
    depends_on:
      - mongodb
      - opcua-server
    environment:
      - OPCUA_SERVER_URL=opc.tcp://opcua-server:4840
      - MONGODB_CONNECTION_STRING=*****************************************/
      - MONGODB_DATABASE=datalogger
      - MONGODB_COLLECTION=sensor_data
      - LOG_LEVEL=INFO
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./.env:/app/.env
    networks:
      - datalogger-network

  # MongoDB Express (Web UI for MongoDB)
  mongo-express:
    image: mongo-express:latest
    container_name: datalogger-mongo-express
    restart: unless-stopped
    depends_on:
      - mongodb
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    networks:
      - datalogger-network

volumes:
  mongodb_data:

networks:
  datalogger-network:
    driver: bridge
