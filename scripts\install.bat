@echo off
REM OPC UA Data Logger Installation Script for Windows
REM This script sets up the data logger environment on Windows

setlocal enabledelayedexpansion

echo ========================================
echo OPC UA Data Logger Installation
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later from https://python.org
    pause
    exit /b 1
)

echo Python found:
python --version

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo pip found:
pip --version
echo.

REM Create virtual environment
echo Creating virtual environment...
if exist venv (
    echo Virtual environment already exists, removing old one...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

echo Virtual environment created successfully
echo.

REM Activate virtual environment and install dependencies
echo Installing dependencies...
call venv\Scripts\activate.bat

python -m pip install --upgrade pip
if errorlevel 1 (
    echo ERROR: Failed to upgrade pip
    pause
    exit /b 1
)

pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo Dependencies installed successfully
echo.

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Copy environment file if it doesn't exist
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo Environment file created from example
    ) else (
        echo Creating default environment file...
        echo # OPC UA Server Configuration > .env
        echo OPCUA_SERVER_URL=opc.tcp://localhost:4840 >> .env
        echo OPCUA_USERNAME= >> .env
        echo OPCUA_PASSWORD= >> .env
        echo. >> .env
        echo # MongoDB Configuration >> .env
        echo MONGODB_CONNECTION_STRING=mongodb://localhost:27017/ >> .env
        echo MONGODB_DATABASE=datalogger >> .env
        echo MONGODB_COLLECTION=sensor_data >> .env
        echo. >> .env
        echo # Logging Configuration >> .env
        echo LOG_LEVEL=INFO >> .env
        echo LOG_ROTATION=1 day >> .env
        echo LOG_RETENTION=30 days >> .env
        echo. >> .env
        echo # Data Logger Settings >> .env
        echo SAMPLING_INTERVAL=5 >> .env
        echo BUFFER_SIZE=1000 >> .env
        echo FLUSH_INTERVAL=30 >> .env
    )
) else (
    echo Environment file already exists
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Edit configuration: config\config.yaml
echo 2. Edit environment variables: .env
echo 3. Test configuration: venv\Scripts\python main.py --validate
echo 4. Test connections: venv\Scripts\python main.py --test-connections
echo 5. Start the data logger: venv\Scripts\python main.py
echo.
echo To run the data logger:
echo   venv\Scripts\activate
echo   python main.py
echo.
echo To run as Windows service, see scripts\install-service.bat
echo.

pause
