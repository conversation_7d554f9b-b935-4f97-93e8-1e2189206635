#!/usr/bin/env python3
"""
OPC UA to MongoDB Data Logger - Main Application

This is the main entry point for the data logger application.
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.datalogger import DataLogger, create_datalogger
from src.config import ConfigManager
from loguru import logger


async def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(
        description="OPC UA to MongoDB Data Logger",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Use default config
  python main.py -c config/custom.yaml    # Use custom config
  python main.py --validate               # Validate config only
  python main.py --test-connections       # Test connections only
        """
    )
    
    parser.add_argument(
        "-c", "--config",
        default="config/config.yaml",
        help="Path to configuration file (default: config/config.yaml)"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate configuration and exit"
    )
    
    parser.add_argument(
        "--test-connections",
        action="store_true",
        help="Test connections to OPC UA and MongoDB, then exit"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Override log level from configuration"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="OPC UA Data Logger v1.0.0"
    )
    
    args = parser.parse_args()
    
    try:
        # Load and validate configuration
        config_manager = ConfigManager(args.config)
        
        if args.validate:
            logger.info("Validating configuration...")
            config = config_manager.load_config()
            logger.info("✓ Configuration is valid")
            return 0
        
        # Load configuration
        config = config_manager.load_config()
        
        # Override log level if specified
        if args.log_level:
            config.logging.level = args.log_level
        
        if args.test_connections:
            logger.info("Testing connections...")
            success = await test_connections(config)
            return 0 if success else 1
        
        # Run the data logger
        logger.info("Starting OPC UA Data Logger...")
        
        async with create_datalogger(args.config) as datalogger:
            await datalogger.start()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
        return 0
    except FileNotFoundError as e:
        logger.error(f"Configuration file not found: {e}")
        return 1
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


async def test_connections(config):
    """
    Test connections to OPC UA server and MongoDB
    
    Args:
        config: Application configuration
    
    Returns:
        bool: True if all connections successful
    """
    from src.opcua_client import OPCUAClient
    from src.mongodb_client import MongoDBClient
    
    success = True
    
    # Test MongoDB connection
    logger.info("Testing MongoDB connection...")
    mongodb_client = MongoDBClient(config.mongodb)
    
    try:
        if mongodb_client.connect():
            logger.info("✓ MongoDB connection successful")
            
            # Test basic operations
            stats = mongodb_client.get_statistics()
            logger.info(f"  Database: {config.mongodb.database}")
            logger.info(f"  Collection: {config.mongodb.collection}")
            logger.info(f"  Documents: {stats.get('document_count', 0)}")
            
            mongodb_client.disconnect()
        else:
            logger.error("✗ MongoDB connection failed")
            success = False
    except Exception as e:
        logger.error(f"✗ MongoDB connection error: {e}")
        success = False
    
    # Test OPC UA connection
    logger.info("Testing OPC UA connection...")
    
    def dummy_callback(node_name, value, timestamp):
        pass
    
    opcua_client = OPCUAClient(config.opcua, dummy_callback)
    
    try:
        if await opcua_client.connect():
            logger.info("✓ OPC UA connection successful")
            
            # Get server info
            server_info = await opcua_client.get_server_info()
            logger.info(f"  Server URL: {config.opcua.server_url}")
            logger.info(f"  Server State: {server_info.get('state', 'Unknown')}")
            
            # Test reading nodes
            if config.opcua.nodes:
                logger.info("Testing node reads...")
                data = await opcua_client.read_nodes_once()
                
                for node_name, node_data in data.items():
                    if 'error' in node_data:
                        logger.warning(f"  ✗ {node_name}: {node_data['error']}")
                    else:
                        logger.info(f"  ✓ {node_name}: {node_data['value']}")
            
            await opcua_client.disconnect()
        else:
            logger.error("✗ OPC UA connection failed")
            success = False
    except Exception as e:
        logger.error(f"✗ OPC UA connection error: {e}")
        success = False
    
    if success:
        logger.info("✓ All connection tests passed")
    else:
        logger.error("✗ Some connection tests failed")
    
    return success


def run_cli():
    """CLI entry point for running the data logger"""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run_cli()
