#!/bin/bash

# OPC UA Data Logger Installation Script
# This script installs the data logger as a system service on Linux

set -e

# Configuration
INSTALL_DIR="/opt/opcua-datalogger"
SERVICE_USER="datalogger"
SERVICE_NAME="opcua-datalogger"
PYTHON_VERSION="3.11"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# Install system dependencies
install_dependencies() {
    log_info "Installing system dependencies..."
    
    if command -v apt-get &> /dev/null; then
        apt-get update
        apt-get install -y python3 python3-pip python3-venv git curl
    elif command -v yum &> /dev/null; then
        yum update -y
        yum install -y python3 python3-pip git curl
    elif command -v dnf &> /dev/null; then
        dnf update -y
        dnf install -y python3 python3-pip git curl
    else
        log_error "Unsupported package manager. Please install Python 3.8+ manually."
        exit 1
    fi
}

# Create service user
create_user() {
    log_info "Creating service user: $SERVICE_USER"
    
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd --system --home-dir "$INSTALL_DIR" --shell /bin/false "$SERVICE_USER"
        log_info "User $SERVICE_USER created"
    else
        log_warn "User $SERVICE_USER already exists"
    fi
}

# Create installation directory
create_directories() {
    log_info "Creating installation directories..."
    
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$INSTALL_DIR/logs"
    mkdir -p "$INSTALL_DIR/config"
    
    chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
}

# Install application
install_application() {
    log_info "Installing application to $INSTALL_DIR..."
    
    # Copy application files
    cp -r src/ "$INSTALL_DIR/"
    cp main.py "$INSTALL_DIR/"
    cp requirements.txt "$INSTALL_DIR/"
    cp README.md "$INSTALL_DIR/"
    
    # Copy configuration files
    if [ -f "config/config.yaml" ]; then
        cp config/config.yaml "$INSTALL_DIR/config/"
    fi
    
    if [ -f ".env.example" ]; then
        cp .env.example "$INSTALL_DIR/"
    fi
    
    # Set permissions
    chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR/main.py"
}

# Create Python virtual environment
create_venv() {
    log_info "Creating Python virtual environment..."
    
    cd "$INSTALL_DIR"
    sudo -u "$SERVICE_USER" python3 -m venv venv
    sudo -u "$SERVICE_USER" ./venv/bin/pip install --upgrade pip
    sudo -u "$SERVICE_USER" ./venv/bin/pip install -r requirements.txt
}

# Install systemd service
install_service() {
    log_info "Installing systemd service..."
    
    # Copy service file
    cp scripts/opcua-datalogger.service /etc/systemd/system/
    
    # Reload systemd
    systemctl daemon-reload
    
    # Enable service
    systemctl enable "$SERVICE_NAME"
    
    log_info "Service installed and enabled"
}

# Create configuration
create_config() {
    log_info "Creating default configuration..."
    
    if [ ! -f "$INSTALL_DIR/config/config.yaml" ]; then
        cat > "$INSTALL_DIR/config/config.yaml" << EOF
# OPC UA Server Configuration
opcua:
  server_url: "opc.tcp://localhost:4840"
  security_policy: "None"
  security_mode: "None"
  username: null
  password: null
  
  subscription:
    publishing_interval: 1000
    lifetime_count: 6000
    max_keepalive_count: 3000
    max_notifications_per_publish: 0
    priority: 0

  nodes:
    - node_id: "ns=2;i=2"
      name: "Temperature"
      data_type: "float"
    - node_id: "ns=2;i=3"
      name: "Pressure"
      data_type: "float"

# MongoDB Configuration
mongodb:
  connection_string: "mongodb://localhost:27017/"
  database: "datalogger"
  collection: "sensor_data"
  
  max_pool_size: 10
  min_pool_size: 1
  max_idle_time_ms: 30000
  
  write_concern:
    w: 1
    j: true
    wtimeout: 5000

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "30 days"
  compression: "gz"
  
  files:
    main: "logs/datalogger.log"
    opcua: "logs/opcua.log"
    mongodb: "logs/mongodb.log"

# Data Logger Settings
datalogger:
  sampling_interval: 5
  buffer_size: 1000
  flush_interval: 30
  max_retries: 3
  retry_delay: 5
  validate_data: true
  skip_invalid_data: true
  use_server_timestamp: true
  timezone: "UTC"
EOF
        
        chown "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR/config/config.yaml"
        log_info "Default configuration created at $INSTALL_DIR/config/config.yaml"
    else
        log_warn "Configuration file already exists, skipping creation"
    fi
}

# Create environment file
create_env_file() {
    if [ ! -f "$INSTALL_DIR/.env" ]; then
        log_info "Creating environment file..."
        
        cat > "$INSTALL_DIR/.env" << EOF
# OPC UA Server Configuration
OPCUA_SERVER_URL=opc.tcp://localhost:4840
OPCUA_USERNAME=
OPCUA_PASSWORD=

# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
MONGODB_DATABASE=datalogger
MONGODB_COLLECTION=sensor_data

# Logging Configuration
LOG_LEVEL=INFO
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# Data Logger Settings
SAMPLING_INTERVAL=5
BUFFER_SIZE=1000
FLUSH_INTERVAL=30
EOF
        
        chown "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR/.env"
        log_info "Environment file created at $INSTALL_DIR/.env"
    else
        log_warn "Environment file already exists, skipping creation"
    fi
}

# Main installation function
main() {
    log_info "Starting OPC UA Data Logger installation..."
    
    check_root
    install_dependencies
    create_user
    create_directories
    install_application
    create_venv
    create_config
    create_env_file
    install_service
    
    log_info "Installation completed successfully!"
    echo
    log_info "Next steps:"
    echo "1. Edit configuration: $INSTALL_DIR/config/config.yaml"
    echo "2. Edit environment: $INSTALL_DIR/.env"
    echo "3. Test configuration: sudo -u $SERVICE_USER $INSTALL_DIR/venv/bin/python $INSTALL_DIR/main.py --validate"
    echo "4. Test connections: sudo -u $SERVICE_USER $INSTALL_DIR/venv/bin/python $INSTALL_DIR/main.py --test-connections"
    echo "5. Start service: systemctl start $SERVICE_NAME"
    echo "6. Check status: systemctl status $SERVICE_NAME"
    echo "7. View logs: journalctl -u $SERVICE_NAME -f"
}

# Run main function
main "$@"
