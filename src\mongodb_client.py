"""
MongoDB Client Module

This module provides functionality to connect to MongoDB and store data.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from dataclasses import dataclass, asdict

import pymongo
from pymongo import MongoClient, WriteConcern
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, BulkWriteError
from pydantic import BaseModel, Field


class MongoDBConfig(BaseModel):
    """MongoDB Configuration"""
    connection_string: str = Field(..., description="MongoDB connection string")
    database: str = Field(..., description="Database name")
    collection: str = Field(..., description="Collection name")
    
    max_pool_size: int = Field(default=10, description="Maximum connection pool size")
    min_pool_size: int = Field(default=1, description="Minimum connection pool size")
    max_idle_time_ms: int = Field(default=30000, description="Maximum idle time in milliseconds")
    
    write_concern: Dict[str, Any] = Field(default_factory=lambda: {
        "w": 1,
        "j": True,
        "wtimeout": 5000
    })


@dataclass
class SensorData:
    """Data structure for sensor readings"""
    node_name: str
    value: Any
    timestamp: datetime
    node_id: Optional[str] = None
    data_type: Optional[str] = None
    quality: Optional[str] = None
    source: str = "opcua"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for MongoDB storage"""
        data = asdict(self)
        # Ensure timestamp is timezone-aware
        if self.timestamp.tzinfo is None:
            data['timestamp'] = self.timestamp.replace(tzinfo=timezone.utc)
        return data


class DataBuffer:
    """Buffer for batching data writes"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.buffer: List[SensorData] = []
        self.logger = logging.getLogger(__name__)
    
    def add(self, data: SensorData) -> bool:
        """
        Add data to buffer
        
        Returns:
            bool: True if buffer is full and needs flushing
        """
        self.buffer.append(data)
        return len(self.buffer) >= self.max_size
    
    def get_and_clear(self) -> List[SensorData]:
        """Get all buffered data and clear the buffer"""
        data = self.buffer.copy()
        self.buffer.clear()
        return data
    
    def size(self) -> int:
        """Get current buffer size"""
        return len(self.buffer)
    
    def is_empty(self) -> bool:
        """Check if buffer is empty"""
        return len(self.buffer) == 0


class MongoDBClient:
    """MongoDB Client for data storage"""
    
    def __init__(self, config: MongoDBConfig):
        """
        Initialize MongoDB Client
        
        Args:
            config: MongoDB configuration
        """
        self.config = config
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
        self.logger = logging.getLogger(__name__)
        self.is_connected = False
        
        # Data buffer for batch writes
        self.buffer = DataBuffer()
    
    def connect(self) -> bool:
        """
        Connect to MongoDB
        
        Returns:
            bool: True if connection successful
        """
        try:
            self.logger.info(f"Connecting to MongoDB: {self.config.database}")
            
            # Create MongoDB client with configuration
            self.client = MongoClient(
                self.config.connection_string,
                maxPoolSize=self.config.max_pool_size,
                minPoolSize=self.config.min_pool_size,
                maxIdleTimeMS=self.config.max_idle_time_ms,
                serverSelectionTimeoutMS=5000  # 5 second timeout
            )
            
            # Test connection
            self.client.admin.command('ping')
            
            # Get database and collection
            self.database = self.client[self.config.database]
            
            # Set write concern
            write_concern = WriteConcern(**self.config.write_concern)
            self.collection = self.database.get_collection(
                self.config.collection,
                write_concern=write_concern
            )
            
            self.is_connected = True
            self.logger.info("Successfully connected to MongoDB")
            
            # Create indexes for better performance
            self._create_indexes()
            
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            self.logger.error(f"Failed to connect to MongoDB: {e}")
            self.is_connected = False
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error connecting to MongoDB: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Disconnect from MongoDB"""
        try:
            # Flush any remaining data in buffer
            if not self.buffer.is_empty():
                self.flush_buffer()
            
            if self.client:
                self.client.close()
                self.is_connected = False
                self.logger.info("Disconnected from MongoDB")
                
        except Exception as e:
            self.logger.error(f"Error during disconnect: {e}")
    
    def _create_indexes(self):
        """Create indexes for better query performance"""
        try:
            # Index on timestamp for time-based queries
            self.collection.create_index([("timestamp", pymongo.DESCENDING)])
            
            # Index on node_name for filtering by sensor
            self.collection.create_index([("node_name", pymongo.ASCENDING)])
            
            # Compound index for node_name and timestamp
            self.collection.create_index([
                ("node_name", pymongo.ASCENDING),
                ("timestamp", pymongo.DESCENDING)
            ])
            
            self.logger.info("Created MongoDB indexes")
            
        except Exception as e:
            self.logger.warning(f"Failed to create indexes: {e}")
    
    def store_data(self, node_name: str, value: Any, timestamp: datetime, 
                   node_id: Optional[str] = None, **kwargs) -> bool:
        """
        Store single data point
        
        Args:
            node_name: Name of the sensor/node
            value: Sensor value
            timestamp: Timestamp of the reading
            node_id: OPC UA node ID
            **kwargs: Additional metadata
        
        Returns:
            bool: True if storage successful
        """
        if not self.is_connected:
            self.logger.error("Not connected to MongoDB")
            return False
        
        try:
            sensor_data = SensorData(
                node_name=node_name,
                value=value,
                timestamp=timestamp,
                node_id=node_id,
                **kwargs
            )
            
            # Add to buffer
            should_flush = self.buffer.add(sensor_data)
            
            # Flush if buffer is full
            if should_flush:
                return self.flush_buffer()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing data: {e}")
            return False
    
    def store_batch(self, data_points: List[Dict[str, Any]]) -> bool:
        """
        Store multiple data points at once
        
        Args:
            data_points: List of data point dictionaries
        
        Returns:
            bool: True if storage successful
        """
        if not self.is_connected:
            self.logger.error("Not connected to MongoDB")
            return False
        
        if not data_points:
            return True
        
        try:
            # Convert to SensorData objects and add to buffer
            for point in data_points:
                sensor_data = SensorData(**point)
                self.buffer.add(sensor_data)
            
            # Flush buffer
            return self.flush_buffer()
            
        except Exception as e:
            self.logger.error(f"Error storing batch data: {e}")
            return False
    
    def flush_buffer(self) -> bool:
        """
        Flush buffered data to MongoDB
        
        Returns:
            bool: True if flush successful
        """
        if not self.is_connected or self.buffer.is_empty():
            return True
        
        try:
            data_to_write = self.buffer.get_and_clear()
            documents = [data.to_dict() for data in data_to_write]
            
            result = self.collection.insert_many(documents, ordered=False)
            
            self.logger.info(f"Successfully wrote {len(result.inserted_ids)} documents to MongoDB")
            return True
            
        except BulkWriteError as e:
            self.logger.error(f"Bulk write error: {e.details}")
            # Some documents might have been inserted successfully
            return False
        except Exception as e:
            self.logger.error(f"Error flushing buffer: {e}")
            return False
    
    def get_latest_data(self, node_name: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get latest data points
        
        Args:
            node_name: Filter by node name (optional)
            limit: Maximum number of records to return
        
        Returns:
            List of data points
        """
        if not self.is_connected:
            return []
        
        try:
            query = {}
            if node_name:
                query["node_name"] = node_name
            
            cursor = self.collection.find(query).sort("timestamp", -1).limit(limit)
            return list(cursor)
            
        except Exception as e:
            self.logger.error(f"Error retrieving data: {e}")
            return []
    
    def get_data_range(self, start_time: datetime, end_time: datetime, 
                       node_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get data within time range
        
        Args:
            start_time: Start timestamp
            end_time: End timestamp
            node_name: Filter by node name (optional)
        
        Returns:
            List of data points
        """
        if not self.is_connected:
            return []
        
        try:
            query = {
                "timestamp": {
                    "$gte": start_time,
                    "$lte": end_time
                }
            }
            
            if node_name:
                query["node_name"] = node_name
            
            cursor = self.collection.find(query).sort("timestamp", 1)
            return list(cursor)
            
        except Exception as e:
            self.logger.error(f"Error retrieving data range: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get collection statistics"""
        if not self.is_connected:
            return {}
        
        try:
            stats = self.database.command("collStats", self.config.collection)
            return {
                "document_count": stats.get("count", 0),
                "size_bytes": stats.get("size", 0),
                "avg_document_size": stats.get("avgObjSize", 0),
                "storage_size": stats.get("storageSize", 0),
                "indexes": stats.get("nindexes", 0)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting statistics: {e}")
            return {}
