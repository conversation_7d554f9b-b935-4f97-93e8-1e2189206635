# OPC UA to MongoDB Data Logger

A robust Python application for collecting data from OPC UA servers and storing it in MongoDB with real-time monitoring, buffering, and error handling capabilities.

## Features

- **OPC UA Client**: Connect to OPC UA servers with support for various security policies
- **MongoDB Storage**: Efficient data storage with buffering and batch writes
- **Real-time Monitoring**: Subscribe to OPC UA node changes for real-time data collection
- **Configuration Management**: Flexible YAML-based configuration with environment variable overrides
- **Robust Error Handling**: Automatic reconnection, retry mechanisms, and graceful error recovery
- **Logging**: Comprehensive logging with rotation and multiple output formats
- **Health Monitoring**: Periodic health checks and statistics reporting
- **Data Export**: Built-in tools for exporting data to CSV and JSON formats
- **CLI Tools**: Command-line utilities for configuration validation and data management

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd datalogger

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit the configuration file `config/config.yaml` to match your OPC UA server and MongoDB settings.

### 3. Run the Data Logger

```bash
# Validate configuration
python main.py --validate

# Test connections
python main.py --test-connections

# Start the data logger
python main.py
```

## Configuration

### Basic Configuration

The main configuration is stored in `config/config.yaml`. Here's a minimal example:

```yaml
opcua:
  server_url: "opc.tcp://localhost:4840"
  nodes:
    - node_id: "ns=2;i=2"
      name: "Temperature"
      data_type: "float"
    - node_id: "ns=2;i=3"
      name: "Pressure"
      data_type: "float"

mongodb:
  connection_string: "mongodb://localhost:27017/"
  database: "datalogger"
  collection: "sensor_data"

datalogger:
  sampling_interval: 5
  buffer_size: 1000
  flush_interval: 30
```

### Environment Variables

You can override configuration values using environment variables:

```bash
export OPCUA_SERVER_URL="opc.tcp://your-server:4840"
export MONGODB_CONNECTION_STRING="mongodb://your-mongodb:27017/"
export LOG_LEVEL="DEBUG"
```

### OPC UA Security

For secure connections, configure certificates and authentication:

```yaml
opcua:
  server_url: "opc.tcp://secure-server:4840"
  security_policy: "Basic256Sha256"
  security_mode: "SignAndEncrypt"
  username: "your_username"
  password: "your_password"
  certificate_path: "certs/client_cert.pem"
  private_key_path: "certs/client_key.pem"
```

## Usage

### Command Line Options

```bash
# Basic usage
python main.py

# Use custom configuration
python main.py -c config/custom.yaml

# Validate configuration only
python main.py --validate

# Test connections without starting logger
python main.py --test-connections

# Override log level
python main.py --log-level DEBUG
```

### Data Export

Export collected data to various formats:

```bash
# Export to CSV
python -m src.utils export -o data.csv -f csv

# Export specific node data
python -m src.utils export -o temperature.csv -n Temperature

# Export data from last 7 days
python -m src.utils export -o recent.csv --days 7

# Export data within date range
python -m src.utils export -o range.csv --start "2024-01-01 00:00:00" --end "2024-01-31 23:59:59"
```

### Configuration Management

```bash
# Create sample configuration
python -m src.utils create-config -o config/new_config.yaml

# Validate configuration file
python -m src.utils validate-config config/config.yaml
```

## Data Structure

Data is stored in MongoDB with the following structure:

```json
{
  "_id": "ObjectId",
  "node_name": "Temperature",
  "value": 25.6,
  "timestamp": "2024-01-15T10:30:00Z",
  "node_id": "ns=2;i=2",
  "data_type": "float",
  "source": "opcua"
}
```

## Architecture

The application consists of several key components:

- **OPCUAClient**: Handles connection and data subscription from OPC UA servers
- **MongoDBClient**: Manages MongoDB connections and data storage with buffering
- **DataLogger**: Coordinates between OPC UA and MongoDB clients
- **ConfigManager**: Handles configuration loading and validation
- **Scheduler**: Manages periodic tasks like buffer flushing and health checks

## Monitoring and Logging

### Log Files

- `logs/datalogger.log`: Main application logs
- `logs/opcua.log`: OPC UA specific logs
- `logs/mongodb.log`: MongoDB specific logs

### Health Monitoring

The application includes built-in health monitoring:

- Connection status checks every minute
- Automatic reconnection attempts
- Buffer flush monitoring
- Statistics logging every 5 minutes

### Statistics

View real-time statistics:

```python
from src.mongodb_client import MongoDBClient
from src.config import load_config

config = load_config()
client = MongoDBClient(config.mongodb)
client.connect()

stats = client.get_statistics()
print(f"Documents: {stats['document_count']}")
print(f"Storage size: {stats['storage_size']} bytes")
```

## Error Handling

The application includes comprehensive error handling:

- **Connection Failures**: Automatic reconnection with exponential backoff
- **Data Validation**: Invalid data points are logged and optionally skipped
- **Buffer Management**: Automatic buffer flushing prevents data loss
- **Graceful Shutdown**: Proper cleanup on application termination

## Development

### Project Structure

```
datalogger/
├── src/
│   ├── __init__.py
│   ├── config.py          # Configuration management
│   ├── opcua_client.py    # OPC UA client implementation
│   ├── mongodb_client.py  # MongoDB client implementation
│   ├── datalogger.py      # Main data logger coordination
│   └── utils.py           # Utility functions and CLI tools
├── config/
│   └── config.yaml        # Main configuration file
├── logs/                  # Log files directory
├── main.py               # Application entry point
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables example
└── README.md           # This file
```

### Adding New Features

1. **Custom Data Processing**: Extend the `_handle_opcua_data` method in `DataLogger`
2. **Additional Storage Backends**: Implement new client classes following the MongoDB client pattern
3. **Custom Schedulers**: Add new periodic tasks in the `_setup_scheduler` method

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if OPC UA server and MongoDB are running
2. **Authentication Errors**: Verify credentials and security settings
3. **Certificate Issues**: Ensure certificate paths are correct and certificates are valid
4. **Memory Usage**: Adjust buffer size and flush interval for your use case

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
python main.py --log-level DEBUG
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For support and questions:

- Check the logs in the `logs/` directory
- Use `--test-connections` to verify connectivity
- Enable debug logging for detailed information
- Review the configuration documentation above
