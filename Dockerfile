FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY main.py .

# Create logs directory
RUN mkdir -p logs

# Create non-root user
RUN useradd -m -u 1000 datalogger && \
    chown -R datalogger:datalogger /app

USER datalogger

# Expose port (if needed for monitoring)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python main.py --test-connections || exit 1

# Default command
CMD ["python", "main.py"]
